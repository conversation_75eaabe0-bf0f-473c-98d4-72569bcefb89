<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat App Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 Chat App Debug</h1>
        <p>This page helps debug initialization issues.</p>

        <div id="status"></div>
        
        <h3>Script Loading Status:</h3>
        <div id="scriptStatus"></div>
        
        <h3>Manager Status:</h3>
        <div id="managerStatus"></div>
        
        <h3>Console Logs:</h3>
        <pre id="consoleLogs"></pre>
        
        <button onclick="runDebug()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
            Run Debug Check
        </button>
        
        <button onclick="location.href='index.html'" style="background: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-left: 10px;">
            Go to Chat App
        </button>
    </div>

    <!-- Load scripts in order -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore-compat.js"></script>
    
    <script>
        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        let logs = [];
        
        console.log = function(...args) {
            logs.push('LOG: ' + args.join(' '));
            originalLog.apply(console, args);
            updateLogs();
        };
        
        console.error = function(...args) {
            logs.push('ERROR: ' + args.join(' '));
            originalError.apply(console, args);
            updateLogs();
        };
        
        console.warn = function(...args) {
            logs.push('WARN: ' + args.join(' '));
            originalWarn.apply(console, args);
            updateLogs();
        };
        
        function updateLogs() {
            document.getElementById('consoleLogs').textContent = logs.slice(-20).join('\n');
        }
        
        function updateStatus(id, message, type = 'info') {
            const element = document.getElementById(id);
            element.innerHTML = `<div class="${type}">${message}</div>`;
        }
        
        function runDebug() {
            updateStatus('status', 'Running debug checks...', 'info');
            
            // Check script loading
            let scriptStatus = '';
            
            if (typeof io !== 'undefined') {
                scriptStatus += '✅ Socket.IO loaded<br>';
            } else {
                scriptStatus += '❌ Socket.IO not loaded<br>';
            }
            
            if (typeof firebase !== 'undefined') {
                scriptStatus += '✅ Firebase loaded<br>';
            } else {
                scriptStatus += '❌ Firebase not loaded<br>';
            }
            
            document.getElementById('scriptStatus').innerHTML = scriptStatus;
            
            // Load and check our scripts
            loadAppScripts();
        }
        
        function loadAppScripts() {
            const scripts = [
                'js/config.js',
                'js/storage.js',
                'js/auth.js',
                'js/ui.js',
                'js/chat.js',
                'js/app.js'
            ];
            
            let loadedCount = 0;
            let managerStatus = '';
            
            scripts.forEach((src, index) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = function() {
                    loadedCount++;
                    console.log(`Loaded: ${src}`);
                    
                    if (loadedCount === scripts.length) {
                        // All scripts loaded, check managers
                        setTimeout(() => {
                            checkManagers();
                        }, 1000);
                    }
                };
                script.onerror = function() {
                    console.error(`Failed to load: ${src}`);
                    managerStatus += `❌ Failed to load ${src}<br>`;
                    document.getElementById('managerStatus').innerHTML = managerStatus;
                };
                document.head.appendChild(script);
            });
        }
        
        function checkManagers() {
            let managerStatus = '';
            
            if (window.APP_CONFIG) {
                managerStatus += '✅ APP_CONFIG loaded<br>';
            } else {
                managerStatus += '❌ APP_CONFIG not loaded<br>';
            }
            
            if (window.StorageManager) {
                managerStatus += '✅ StorageManager loaded<br>';
            } else {
                managerStatus += '❌ StorageManager not loaded<br>';
            }
            
            if (window.AuthManager) {
                managerStatus += '✅ AuthManager loaded<br>';
            } else {
                managerStatus += '❌ AuthManager not loaded<br>';
            }
            
            if (window.UIManager) {
                managerStatus += '✅ UIManager loaded<br>';
            } else {
                managerStatus += '❌ UIManager not loaded<br>';
            }
            
            if (window.ChatManager) {
                managerStatus += '✅ ChatManager loaded<br>';
            } else {
                managerStatus += '❌ ChatManager not loaded<br>';
            }
            
            if (window.ChatApp) {
                managerStatus += '✅ ChatApp loaded<br>';
            } else {
                managerStatus += '❌ ChatApp not loaded<br>';
            }
            
            document.getElementById('managerStatus').innerHTML = managerStatus;
            
            // Final status
            const allLoaded = window.APP_CONFIG && window.StorageManager && 
                            window.AuthManager && window.UIManager && 
                            window.ChatManager && window.ChatApp;
            
            if (allLoaded) {
                updateStatus('status', '✅ All components loaded successfully! The initialization error should be fixed.', 'success');
            } else {
                updateStatus('status', '❌ Some components failed to load. Check the console for details.', 'error');
            }
        }
        
        // Auto-run debug on page load
        window.addEventListener('load', () => {
            setTimeout(runDebug, 500);
        });
    </script>
</body>
</html>
