// Local Storage Management
class StorageManager {
    // User Data Management
    static setUserData(userData) {
        try {
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.USER_DATA, JSON.stringify(userData));
        } catch (error) {
            console.error('Error saving user data:', error);
        }
    }

    static getUserData() {
        try {
            const data = localStorage.getItem(APP_CONFIG.STORAGE_KEYS.USER_DATA);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error getting user data:', error);
            return null;
        }
    }

    static clearUserData() {
        try {
            localStorage.removeItem(APP_CONFIG.STORAGE_KEYS.USER_DATA);
        } catch (error) {
            console.error('Error clearing user data:', error);
        }
    }

    // Messages Management
    static saveMessage(chatId, message) {
        try {
            const messages = this.getMessages(chatId);
            messages.push({
                ...message,
                id: this.generateId(),
                timestamp: new Date().toISOString()
            });
            
            const allMessages = this.getAllMessages();
            allMessages[chatId] = messages;
            
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.MESSAGES, JSON.stringify(allMessages));
            return messages[messages.length - 1];
        } catch (error) {
            console.error('Error saving message:', error);
            return null;
        }
    }

    static getMessages(chatId) {
        try {
            const allMessages = this.getAllMessages();
            return allMessages[chatId] || [];
        } catch (error) {
            console.error('Error getting messages:', error);
            return [];
        }
    }

    static getAllMessages() {
        try {
            const data = localStorage.getItem(APP_CONFIG.STORAGE_KEYS.MESSAGES);
            return data ? JSON.parse(data) : {};
        } catch (error) {
            console.error('Error getting all messages:', error);
            return {};
        }
    }

    static clearMessages(chatId) {
        try {
            const allMessages = this.getAllMessages();
            delete allMessages[chatId];
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.MESSAGES, JSON.stringify(allMessages));
        } catch (error) {
            console.error('Error clearing messages:', error);
        }
    }

    // Chats Management
    static saveChat(chat) {
        try {
            const chats = this.getChats();
            const existingIndex = chats.findIndex(c => c.id === chat.id);
            
            if (existingIndex >= 0) {
                chats[existingIndex] = { ...chats[existingIndex], ...chat };
            } else {
                chats.push({
                    ...chat,
                    id: chat.id || this.generateId(),
                    createdAt: new Date().toISOString()
                });
            }
            
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.CHATS, JSON.stringify(chats));
            return chats.find(c => c.id === chat.id);
        } catch (error) {
            console.error('Error saving chat:', error);
            return null;
        }
    }

    static getChats() {
        try {
            const data = localStorage.getItem(APP_CONFIG.STORAGE_KEYS.CHATS);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('Error getting chats:', error);
            return [];
        }
    }

    static getChat(chatId) {
        try {
            const chats = this.getChats();
            return chats.find(chat => chat.id === chatId) || null;
        } catch (error) {
            console.error('Error getting chat:', error);
            return null;
        }
    }

    static deleteChat(chatId) {
        try {
            const chats = this.getChats();
            const filteredChats = chats.filter(chat => chat.id !== chatId);
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.CHATS, JSON.stringify(filteredChats));
            
            // Also clear messages for this chat
            this.clearMessages(chatId);
        } catch (error) {
            console.error('Error deleting chat:', error);
        }
    }

    // Groups Management
    static saveGroup(group) {
        try {
            const groups = this.getGroups();
            const existingIndex = groups.findIndex(g => g.id === group.id);
            
            if (existingIndex >= 0) {
                groups[existingIndex] = { ...groups[existingIndex], ...group };
            } else {
                groups.push({
                    ...group,
                    id: group.id || this.generateId(),
                    createdAt: new Date().toISOString()
                });
            }
            
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.GROUPS, JSON.stringify(groups));
            return groups.find(g => g.id === group.id);
        } catch (error) {
            console.error('Error saving group:', error);
            return null;
        }
    }

    static getGroups() {
        try {
            const data = localStorage.getItem(APP_CONFIG.STORAGE_KEYS.GROUPS);
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('Error getting groups:', error);
            return [];
        }
    }

    static getGroup(groupId) {
        try {
            const groups = this.getGroups();
            return groups.find(group => group.id === groupId) || null;
        } catch (error) {
            console.error('Error getting group:', error);
            return null;
        }
    }

    static deleteGroup(groupId) {
        try {
            const groups = this.getGroups();
            const filteredGroups = groups.filter(group => group.id !== groupId);
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.GROUPS, JSON.stringify(filteredGroups));
            
            // Also clear messages for this group
            this.clearMessages(groupId);
        } catch (error) {
            console.error('Error deleting group:', error);
        }
    }

    // Settings Management
    static saveSettings(settings) {
        try {
            const currentSettings = this.getSettings();
            const updatedSettings = { ...currentSettings, ...settings };
            localStorage.setItem(APP_CONFIG.STORAGE_KEYS.SETTINGS, JSON.stringify(updatedSettings));
        } catch (error) {
            console.error('Error saving settings:', error);
        }
    }

    static getSettings() {
        try {
            const data = localStorage.getItem(APP_CONFIG.STORAGE_KEYS.SETTINGS);
            return data ? JSON.parse(data) : this.getDefaultSettings();
        } catch (error) {
            console.error('Error getting settings:', error);
            return this.getDefaultSettings();
        }
    }

    static getDefaultSettings() {
        return {
            theme: 'light',
            notifications: true,
            soundEnabled: true,
            autoSave: true,
            language: 'en'
        };
    }

    // Utility Methods
    static generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    static exportData() {
        try {
            const data = {
                userData: this.getUserData(),
                messages: this.getAllMessages(),
                chats: this.getChats(),
                groups: this.getGroups(),
                settings: this.getSettings(),
                exportDate: new Date().toISOString()
            };
            return JSON.stringify(data, null, 2);
        } catch (error) {
            console.error('Error exporting data:', error);
            return null;
        }
    }

    static importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            if (data.userData) this.setUserData(data.userData);
            if (data.messages) localStorage.setItem(APP_CONFIG.STORAGE_KEYS.MESSAGES, JSON.stringify(data.messages));
            if (data.chats) localStorage.setItem(APP_CONFIG.STORAGE_KEYS.CHATS, JSON.stringify(data.chats));
            if (data.groups) localStorage.setItem(APP_CONFIG.STORAGE_KEYS.GROUPS, JSON.stringify(data.groups));
            if (data.settings) localStorage.setItem(APP_CONFIG.STORAGE_KEYS.SETTINGS, JSON.stringify(data.settings));
            
            return true;
        } catch (error) {
            console.error('Error importing data:', error);
            return false;
        }
    }

    static clearAllData() {
        try {
            Object.values(APP_CONFIG.STORAGE_KEYS).forEach(key => {
                localStorage.removeItem(key);
            });
        } catch (error) {
            console.error('Error clearing all data:', error);
        }
    }

    // Get storage usage info
    static getStorageInfo() {
        try {
            let totalSize = 0;
            const info = {};
            
            Object.entries(APP_CONFIG.STORAGE_KEYS).forEach(([name, key]) => {
                const data = localStorage.getItem(key);
                const size = data ? new Blob([data]).size : 0;
                info[name] = {
                    size: size,
                    sizeFormatted: this.formatBytes(size)
                };
                totalSize += size;
            });
            
            info.total = {
                size: totalSize,
                sizeFormatted: this.formatBytes(totalSize)
            };
            
            return info;
        } catch (error) {
            console.error('Error getting storage info:', error);
            return null;
        }
    }

    static formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Make StorageManager globally available
window.StorageManager = StorageManager;
