/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

/* Loading Animation */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.screen {
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Login Screen Styles */
.login-container {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-header {
    margin-bottom: 30px;
}

.login-header i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 10px;
}

.login-header h1 {
    color: #333;
    margin-bottom: 5px;
    font-size: 2rem;
}

.login-header p {
    color: #666;
    font-size: 0.9rem;
}

.form-group {
    position: relative;
    margin-bottom: 20px;
}

.form-group input {
    width: 100%;
    padding: 15px 50px 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.form-group i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.btn-primary {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s;
    margin-bottom: 20px;
}

.btn-primary:hover {
    transform: translateY(-2px);
}

.btn-google {
    width: 100%;
    padding: 15px;
    background: white;
    color: #333;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    margin-bottom: 20px;
}

.btn-google:hover {
    border-color: #db4437;
    color: #db4437;
}

.btn-google i {
    margin-right: 10px;
    color: #db4437;
}

.divider {
    position: relative;
    margin: 20px 0;
    text-align: center;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e5e9;
}

.divider span {
    background: white;
    padding: 0 15px;
    color: #999;
    font-size: 0.9rem;
}

.auth-switch {
    color: #666;
    font-size: 0.9rem;
}

.auth-switch span {
    color: #667eea;
    cursor: pointer;
    font-weight: 600;
}

/* Chat Screen Styles */
.chat-container {
    display: flex;
    width: 100%;
    height: 100vh;
    background: white;
}

/* Sidebar Styles */
.sidebar {
    width: 350px;
    background: #f8f9fa;
    border-right: 1px solid #e1e5e9;
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    background: #667eea;
}

.avatar.large {
    width: 80px;
    height: 80px;
}

.user-details h3 {
    font-size: 1rem;
    color: #333;
    margin-bottom: 2px;
}

.status {
    font-size: 0.8rem;
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.status.online {
    background: #d4edda;
    color: #155724;
}

.status.offline {
    background: #f8d7da;
    color: #721c24;
}

.sidebar-actions {
    display: flex;
    gap: 8px;
}

.icon-btn {
    width: 36px;
    height: 36px;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.icon-btn:hover {
    background: #e9ecef;
}

.sidebar-tabs {
    display: flex;
    border-bottom: 1px solid #e1e5e9;
}

.tab-btn {
    flex: 1;
    padding: 15px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
    transition: all 0.2s;
    border-bottom: 2px solid transparent;
}

.tab-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
    background: white;
}

.tab-btn i {
    margin-right: 8px;
}

.sidebar-content {
    flex: 1;
    overflow: hidden;
}

.tab-content {
    height: 100%;
    display: none;
    flex-direction: column;
}

.tab-content.active {
    display: flex;
}

.search-bar {
    position: relative;
    padding: 15px;
}

.search-bar input {
    width: 100%;
    padding: 12px 40px 12px 15px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    font-size: 0.9rem;
}

.search-bar i {
    position: absolute;
    right: 25px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.chats-container,
.users-container,
.groups-container {
    flex: 1;
    overflow-y: auto;
    padding: 0 15px;
}

/* Chat Item Styles */
.chat-item,
.user-item,
.group-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
    margin-bottom: 5px;
}

.chat-item:hover,
.user-item:hover,
.group-item:hover {
    background: #e9ecef;
}

.chat-item.active {
    background: #667eea;
    color: white;
}

.item-info {
    flex: 1;
    margin-left: 12px;
}

.item-name {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.item-message,
.item-status {
    font-size: 0.8rem;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.chat-item.active .item-message {
    color: rgba(255, 255, 255, 0.8);
}

.item-time {
    font-size: 0.7rem;
    color: #999;
}

.chat-item.active .item-time {
    color: rgba(255, 255, 255, 0.8);
}

/* Main Chat Area */
.main-chat {
    flex: 1;
    display: flex;
    flex-direction: column;
    position: relative;
}

.welcome-screen {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
}

.welcome-content {
    text-align: center;
    color: #666;
}

.welcome-content i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #667eea;
}

.welcome-content h2 {
    margin-bottom: 10px;
    color: #333;
}

.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.chat-header {
    padding: 20px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: white;
}

.chat-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.chat-details h3 {
    font-size: 1.1rem;
    color: #333;
    margin-bottom: 2px;
}

.chat-actions {
    display: flex;
    gap: 8px;
}

.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background: #f8f9fa;
}

.message {
    display: flex;
    margin-bottom: 15px;
    max-width: 70%;
}

.message.sent {
    margin-left: auto;
    flex-direction: row-reverse;
}

.message-content {
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: relative;
}

.message.sent .message-content {
    background: #667eea;
    color: white;
}

.message-text {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 4px;
}

.message-time {
    font-size: 0.7rem;
    color: #999;
    text-align: right;
}

.message.sent .message-time {
    color: rgba(255, 255, 255, 0.7);
}

.message-input-container {
    padding: 20px;
    border-top: 1px solid #e1e5e9;
    background: white;
}

.message-input {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 8px 15px;
}

.message-input input {
    flex: 1;
    border: none;
    background: transparent;
    padding: 8px;
    font-size: 0.9rem;
    outline: none;
}

.send-btn {
    background: #667eea;
    color: white;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.send-btn:hover {
    background: #5a6fd8;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e1e5e9;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h2 {
    color: #333;
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #666;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: #f8f9fa;
}

.modal-body {
    padding: 20px;
}

.profile-avatar {
    text-align: center;
    margin-bottom: 20px;
    position: relative;
    display: inline-block;
}

.change-avatar-btn {
    position: absolute;
    bottom: 0;
    right: 0;
    background: #667eea;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    font-size: 0.9rem;
    font-family: inherit;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.modal-actions {
    text-align: center;
    margin-top: 20px;
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: background-color 0.2s;
}

.btn-secondary:hover {
    background: #5a6268;
}

.groups-header {
    padding: 15px;
    border-bottom: 1px solid #e1e5e9;
}

.members-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 10px;
}

.member-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.member-item:hover {
    background: #f8f9fa;
}

.member-item input[type="checkbox"] {
    margin-right: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        position: absolute;
        z-index: 100;
        transform: translateX(-100%);
        transition: transform 0.3s;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-chat {
        width: 100%;
    }
    
    .message {
        max-width: 85%;
    }
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
