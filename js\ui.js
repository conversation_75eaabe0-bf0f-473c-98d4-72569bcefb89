// UI Management
class UIManager {
    constructor() {
        this.currentScreen = 'login';
        this.currentTab = 'chats';
        this.typingTimeouts = new Map();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkAuthState();
    }

    // Setup all event listeners
    setupEventListeners() {
        // Login form events
        document.getElementById('loginBtn').addEventListener('click', this.handleLogin.bind(this));
        document.getElementById('googleLoginBtn').addEventListener('click', this.handleGoogleLogin.bind(this));
        document.getElementById('switchToSignup').addEventListener('click', this.toggleAuthMode.bind(this));

        // Chat events
        document.getElementById('sendBtn').addEventListener('click', this.handleSendMessage.bind(this));
        document.getElementById('messageInput').addEventListener('keypress', this.handleMessageKeyPress.bind(this));
        document.getElementById('messageInput').addEventListener('input', this.handleTyping.bind(this));

        // Sidebar events
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', this.handleTabSwitch.bind(this));
        });

        // Profile and settings
        document.getElementById('profileBtn').addEventListener('click', this.showProfileModal.bind(this));
        document.getElementById('logoutBtn').addEventListener('click', this.handleLogout.bind(this));
        document.getElementById('saveProfileBtn').addEventListener('click', this.handleSaveProfile.bind(this));

        // Group creation
        document.getElementById('createGroupBtn').addEventListener('click', this.showGroupModal.bind(this));
        document.getElementById('createGroupConfirmBtn').addEventListener('click', this.handleCreateGroup.bind(this));

        // Modal close events
        document.querySelectorAll('.close-btn').forEach(btn => {
            btn.addEventListener('click', this.closeModal.bind(this));
        });

        // Search events
        document.getElementById('searchChats').addEventListener('input', this.handleSearchChats.bind(this));
        document.getElementById('searchUsers').addEventListener('input', this.handleSearchUsers.bind(this));

        // Click outside modal to close
        document.addEventListener('click', this.handleModalOutsideClick.bind(this));
    }

    // Authentication handlers
    async handleLogin() {
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;

        if (!email || !password) {
            this.showError('Please fill in all fields');
            return;
        }

        try {
            await AuthManager.signInWithEmail(email, password);
        } catch (error) {
            this.showError(error.message);
        }
    }

    async handleGoogleLogin() {
        try {
            await AuthManager.signInWithGoogle();
        } catch (error) {
            this.showError(error.message);
        }
    }

    async handleLogout() {
        try {
            await AuthManager.signOut();
        } catch (error) {
            this.showError('Error signing out: ' + error.message);
        }
    }

    toggleAuthMode() {
        // Toggle between login and signup modes
        const switchText = document.getElementById('switchToSignup');
        const loginBtn = document.getElementById('loginBtn');
        
        if (switchText.textContent === 'Sign up') {
            switchText.textContent = 'Sign in';
            switchText.parentElement.innerHTML = 'Already have an account? <span id="switchToSignup">Sign in</span>';
            loginBtn.innerHTML = '<i class="fas fa-user-plus"></i> Sign Up';
            document.querySelector('.login-header h1').textContent = 'Create Account';
        } else {
            switchText.textContent = 'Sign up';
            switchText.parentElement.innerHTML = 'Don\'t have an account? <span id="switchToSignup">Sign up</span>';
            loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Sign In';
            document.querySelector('.login-header h1').textContent = 'Chat App';
        }
        
        // Re-attach event listener
        document.getElementById('switchToSignup').addEventListener('click', this.toggleAuthMode.bind(this));
    }

    // Screen management
    showLoadingScreen() {
        document.getElementById('loadingScreen').classList.remove('hidden');
        document.getElementById('loginScreen').classList.add('hidden');
        document.getElementById('chatScreen').classList.add('hidden');
        this.currentScreen = 'loading';
    }

    showLoginScreen() {
        document.getElementById('loadingScreen').classList.add('hidden');
        document.getElementById('loginScreen').classList.remove('hidden');
        document.getElementById('chatScreen').classList.add('hidden');
        this.currentScreen = 'login';
    }

    showChatScreen() {
        document.getElementById('loadingScreen').classList.add('hidden');
        document.getElementById('loginScreen').classList.add('hidden');
        document.getElementById('chatScreen').classList.remove('hidden');
        this.currentScreen = 'chat';
        this.updateUserInfo();
    }

    // Update user info in sidebar
    updateUserInfo() {
        const userData = StorageManager.getUserData();
        if (userData) {
            document.getElementById('userName').textContent = userData.displayName;
            document.getElementById('userAvatar').src = userData.photoURL;
            document.getElementById('userStatus').textContent = userData.status || 'Online';
            document.getElementById('userStatus').className = `status ${userData.status || 'online'}`;
        }
    }

    // Tab management
    handleTabSwitch(event) {
        const tabName = event.currentTarget.dataset.tab;
        
        // Update tab buttons
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        event.currentTarget.classList.add('active');
        
        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
        document.getElementById(`${tabName}List`).classList.add('active');
        
        this.currentTab = tabName;
        
        // Load content based on tab
        if (tabName === 'users') {
            this.loadActiveUsers();
        }
    }

    // Message handling
    async handleSendMessage() {
        const input = document.getElementById('messageInput');
        const text = input.value.trim();
        
        if (!text) return;
        
        input.value = '';
        ChatManager.stopTyping();
        
        await ChatManager.sendMessage(text);
    }

    handleMessageKeyPress(event) {
        if (event.key === 'Enter' && !event.shiftKey) {
            event.preventDefault();
            this.handleSendMessage();
        }
    }

    handleTyping() {
        const input = document.getElementById('messageInput');
        
        if (input.value.trim()) {
            ChatManager.startTyping();
            
            // Clear existing timeout
            if (this.typingTimeout) {
                clearTimeout(this.typingTimeout);
            }
            
            // Set new timeout to stop typing
            this.typingTimeout = setTimeout(() => {
                ChatManager.stopTyping();
            }, 2000);
        } else {
            ChatManager.stopTyping();
        }
    }

    // Chat UI management
    showChatArea(chat, messages) {
        document.getElementById('welcomeScreen').classList.add('hidden');
        document.getElementById('chatArea').classList.remove('hidden');
        
        // Update chat header
        document.getElementById('chatName').textContent = chat.name;
        document.getElementById('chatAvatar').src = chat.avatar || APP_CONFIG.DEFAULT_AVATAR;
        
        // Update status for direct chats
        if (chat.type === APP_CONFIG.CHAT_TYPES.DIRECT) {
            const otherUser = chat.participantDetails.find(p => p.uid !== StorageManager.getUserData().uid);
            if (otherUser) {
                const status = ChatManager.activeUsers.has(otherUser.uid) ? 'Online' : 'Offline';
                document.getElementById('chatStatus').textContent = status;
                document.getElementById('chatStatus').className = `status ${status.toLowerCase()}`;
            }
        } else {
            document.getElementById('chatStatus').textContent = `${chat.participants.length} members`;
            document.getElementById('chatStatus').className = 'status';
        }
        
        // Load messages
        this.loadMessages(messages);
    }

    loadMessages(messages) {
        const container = document.getElementById('messagesContainer');
        container.innerHTML = '';
        
        messages.forEach(message => {
            this.addMessageToChat(message, false);
        });
        
        this.scrollToBottom();
    }

    addMessageToChat(message, scroll = true) {
        const container = document.getElementById('messagesContainer');
        const userData = StorageManager.getUserData();
        const isSent = message.senderId === userData.uid;
        
        const messageElement = document.createElement('div');
        messageElement.className = `message ${isSent ? 'sent' : 'received'}`;
        
        messageElement.innerHTML = `
            <div class="message-content">
                <div class="message-text">${this.escapeHtml(message.text)}</div>
                <div class="message-time">${this.formatTime(message.timestamp)}</div>
            </div>
        `;
        
        container.appendChild(messageElement);
        
        if (scroll) {
            this.scrollToBottom();
        }
    }

    // Chat list management
    populateChatsList(chats) {
        const container = document.getElementById('chatsContainer');
        container.innerHTML = '';
        
        chats.forEach(chat => {
            this.addChatToList(chat);
        });
    }

    addChatToList(chat) {
        const container = document.getElementById('chatsContainer');
        const chatElement = document.createElement('div');
        chatElement.className = 'chat-item';
        chatElement.dataset.chatId = chat.id;
        
        chatElement.innerHTML = `
            <img src="${chat.avatar || APP_CONFIG.DEFAULT_AVATAR}" alt="${chat.name}" class="avatar">
            <div class="item-info">
                <div class="item-name">${this.escapeHtml(chat.name)}</div>
                <div class="item-message">${chat.lastMessage ? this.escapeHtml(chat.lastMessage) : 'No messages yet'}</div>
            </div>
            <div class="item-time">${chat.lastMessageTime ? this.formatTime(chat.lastMessageTime) : ''}</div>
        `;
        
        chatElement.addEventListener('click', () => {
            this.selectChat(chatElement, chat.id);
        });
        
        container.appendChild(chatElement);
    }

    selectChat(element, chatId) {
        // Remove active class from all chats
        document.querySelectorAll('.chat-item').forEach(item => item.classList.remove('active'));
        
        // Add active class to selected chat
        element.classList.add('active');
        
        // Switch to chat
        ChatManager.switchToChat(chatId);
    }

    // User list management
    loadActiveUsers() {
        const users = ChatManager.getActiveUsers();
        this.populateUsersList(users);
    }

    populateUsersList(users) {
        const container = document.getElementById('usersContainer');
        container.innerHTML = '';
        
        const currentUser = StorageManager.getUserData();
        
        users.forEach(user => {
            if (user.uid !== currentUser.uid) {
                this.addUserToList(user);
            }
        });
    }

    addUserToList(user) {
        const container = document.getElementById('usersContainer');
        const userElement = document.createElement('div');
        userElement.className = 'user-item';
        userElement.dataset.userId = user.uid;
        
        userElement.innerHTML = `
            <img src="${user.photoURL || APP_CONFIG.DEFAULT_AVATAR}" alt="${user.displayName}" class="avatar">
            <div class="item-info">
                <div class="item-name">${this.escapeHtml(user.displayName)}</div>
                <div class="item-status">${user.status || 'Online'}</div>
            </div>
        `;
        
        userElement.addEventListener('click', () => {
            this.startDirectChat(user);
        });
        
        container.appendChild(userElement);
    }

    async startDirectChat(user) {
        const chat = await ChatManager.createDirectChat(user);
        if (chat) {
            // Switch to chats tab
            document.querySelector('[data-tab="chats"]').click();
            
            // Select the chat
            const chatElement = document.querySelector(`[data-chat-id="${chat.id}"]`);
            if (chatElement) {
                this.selectChat(chatElement, chat.id);
            }
        }
    }

    // Group management
    populateGroupsList(groups) {
        const container = document.getElementById('groupsContainer');
        container.innerHTML = '';
        
        groups.forEach(group => {
            this.addGroupToList(group);
        });
    }

    addGroupToList(group) {
        const container = document.getElementById('groupsContainer');
        const groupElement = document.createElement('div');
        groupElement.className = 'group-item';
        groupElement.dataset.groupId = group.id;
        
        groupElement.innerHTML = `
            <img src="${group.avatar || APP_CONFIG.DEFAULT_AVATAR}" alt="${group.name}" class="avatar">
            <div class="item-info">
                <div class="item-name">${this.escapeHtml(group.name)}</div>
                <div class="item-message">${group.participants.length} members</div>
            </div>
        `;
        
        groupElement.addEventListener('click', () => {
            this.selectChat(groupElement, group.id);
        });
        
        container.appendChild(groupElement);
    }

    // Modal management
    showProfileModal() {
        const userData = StorageManager.getUserData();
        if (userData) {
            document.getElementById('profileAvatar').src = userData.photoURL;
            document.getElementById('profileName').value = userData.displayName;
            document.getElementById('profileEmail').value = userData.email;
            document.getElementById('profileStatus').value = userData.status || '';
        }
        
        document.getElementById('profileModal').classList.remove('hidden');
    }

    showGroupModal() {
        // Populate members list
        const membersList = document.getElementById('membersList');
        membersList.innerHTML = '';
        
        const activeUsers = ChatManager.getActiveUsers();
        const currentUser = StorageManager.getUserData();
        
        activeUsers.forEach(user => {
            if (user.uid !== currentUser.uid) {
                const memberElement = document.createElement('div');
                memberElement.className = 'member-item';
                memberElement.innerHTML = `
                    <input type="checkbox" id="member_${user.uid}" value="${user.uid}">
                    <img src="${user.photoURL || APP_CONFIG.DEFAULT_AVATAR}" alt="${user.displayName}" class="avatar">
                    <div class="item-info">
                        <div class="item-name">${this.escapeHtml(user.displayName)}</div>
                    </div>
                `;
                membersList.appendChild(memberElement);
            }
        });
        
        document.getElementById('groupModal').classList.remove('hidden');
    }

    closeModal(event) {
        const modalId = event.currentTarget.dataset.modal;
        if (modalId) {
            document.getElementById(modalId).classList.add('hidden');
        }
    }

    handleModalOutsideClick(event) {
        if (event.target.classList.contains('modal')) {
            event.target.classList.add('hidden');
        }
    }

    // Profile and group handlers
    async handleSaveProfile() {
        const name = document.getElementById('profileName').value.trim();
        const status = document.getElementById('profileStatus').value.trim();
        
        if (!name) {
            this.showError('Name is required');
            return;
        }
        
        try {
            await AuthManager.updateProfile({
                displayName: name,
                status: status,
                photoURL: document.getElementById('profileAvatar').src
            });
            
            this.updateUserInfo();
            document.getElementById('profileModal').classList.add('hidden');
            this.showSuccess('Profile updated successfully');
        } catch (error) {
            this.showError('Error updating profile: ' + error.message);
        }
    }

    async handleCreateGroup() {
        const name = document.getElementById('groupName').value.trim();
        const description = document.getElementById('groupDescription').value.trim();
        
        if (!name) {
            this.showError('Group name is required');
            return;
        }
        
        // Get selected members
        const selectedMembers = [];
        document.querySelectorAll('#membersList input[type="checkbox"]:checked').forEach(checkbox => {
            const userId = checkbox.value;
            const user = ChatManager.getActiveUsers().find(u => u.uid === userId);
            if (user) {
                selectedMembers.push(user);
            }
        });
        
        if (selectedMembers.length === 0) {
            this.showError('Please select at least one member');
            return;
        }
        
        try {
            const group = await ChatManager.createGroup(name, description, selectedMembers);
            if (group) {
                document.getElementById('groupModal').classList.add('hidden');
                this.showSuccess('Group created successfully');
                
                // Clear form
                document.getElementById('groupName').value = '';
                document.getElementById('groupDescription').value = '';
            }
        } catch (error) {
            this.showError('Error creating group: ' + error.message);
        }
    }

    // Search handlers
    handleSearchChats(event) {
        const query = event.target.value.toLowerCase();
        const chatItems = document.querySelectorAll('#chatsContainer .chat-item');
        
        chatItems.forEach(item => {
            const name = item.querySelector('.item-name').textContent.toLowerCase();
            const message = item.querySelector('.item-message').textContent.toLowerCase();
            
            if (name.includes(query) || message.includes(query)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    handleSearchUsers(event) {
        const query = event.target.value.toLowerCase();
        const userItems = document.querySelectorAll('#usersContainer .user-item');
        
        userItems.forEach(item => {
            const name = item.querySelector('.item-name').textContent.toLowerCase();
            
            if (name.includes(query)) {
                item.style.display = 'flex';
            } else {
                item.style.display = 'none';
            }
        });
    }

    // Utility methods
    updateConnectionStatus(isConnected) {
        // You can add a connection indicator in the UI
        console.log('Connection status:', isConnected ? 'Connected' : 'Disconnected');
    }

    updateUserStatus(userId, status) {
        // Update user status in the UI
        const userElements = document.querySelectorAll(`[data-user-id="${userId}"]`);
        userElements.forEach(element => {
            const statusElement = element.querySelector('.item-status');
            if (statusElement) {
                statusElement.textContent = status;
            }
        });
    }

    updateActiveUsersList(users) {
        if (this.currentTab === 'users') {
            this.populateUsersList(users);
        }
    }

    updateChatInList(chat) {
        const chatElement = document.querySelector(`[data-chat-id="${chat.id}"]`);
        if (chatElement) {
            const messageElement = chatElement.querySelector('.item-message');
            const timeElement = chatElement.querySelector('.item-time');
            
            if (messageElement) {
                messageElement.textContent = chat.lastMessage || 'No messages yet';
            }
            if (timeElement) {
                timeElement.textContent = chat.lastMessageTime ? this.formatTime(chat.lastMessageTime) : '';
            }
        }
    }

    checkAuthState() {
        // Show loading screen first
        this.showLoadingScreen();

        // Wait a bit for all scripts to load
        setTimeout(() => {
            try {
                const userData = window.StorageManager ? window.StorageManager.getUserData() : null;
                const isSignedIn = window.AuthManager ? window.AuthManager.isSignedIn() : false;

                if (userData && isSignedIn) {
                    this.showChatScreen();
                } else {
                    this.showLoginScreen();
                }
            } catch (error) {
                console.error('Error checking auth state:', error);
                this.showLoginScreen();
            }
        }, 1000); // Give scripts time to load
    }

    // Utility functions
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // Less than 1 minute
            return 'now';
        } else if (diff < 3600000) { // Less than 1 hour
            return Math.floor(diff / 60000) + 'm';
        } else if (diff < 86400000) { // Less than 1 day
            return Math.floor(diff / 3600000) + 'h';
        } else {
            return date.toLocaleDateString();
        }
    }

    scrollToBottom() {
        const container = document.getElementById('messagesContainer');
        container.scrollTop = container.scrollHeight;
    }

    showLoading(message) {
        // You can implement a loading spinner here
        console.log('Loading:', message);
    }

    hideLoading() {
        // Hide loading spinner
        console.log('Loading complete');
    }

    showError(message) {
        // You can implement a toast notification here
        alert('Error: ' + message);
    }

    showSuccess(message) {
        // You can implement a success toast here
        alert('Success: ' + message);
    }
}

// Create global instance
window.UIManager = new UIManager();
