// Main Application Entry Point
class App {
    constructor() {
        this.isInitialized = false;
        this.init();
    }

    async init() {
        try {
            console.log('Initializing Chat App...');
            
            // Wait for DOM to be ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.start());
            } else {
                this.start();
            }
        } catch (error) {
            console.error('Error initializing app:', error);
            this.handleInitError(error);
        }
    }

    start() {
        try {
            // Initialize managers in order
            this.initializeManagers();
            
            // Setup global error handlers
            this.setupErrorHandlers();
            
            // Setup app-level event listeners
            this.setupAppEventListeners();
            
            // Check for updates
            this.checkForUpdates();
            
            this.isInitialized = true;
            console.log('Chat App initialized successfully');
            
        } catch (error) {
            console.error('Error starting app:', error);
            this.handleInitError(error);
        }
    }

    initializeManagers() {
        // Wait for managers to be initialized
        let attempts = 0;
        const maxAttempts = 10;

        const checkManagers = () => {
            attempts++;

            if (window.AuthManager && window.StorageManager && window.ChatManager && window.UIManager) {
                console.log('All managers initialized');
                return true;
            }

            if (attempts >= maxAttempts) {
                console.error('Failed to initialize managers after', maxAttempts, 'attempts');
                this.createFallbackManagers();
                return true;
            }

            console.log(`Waiting for managers... attempt ${attempts}/${maxAttempts}`);
            setTimeout(checkManagers, 100);
            return false;
        };

        return checkManagers();
    }

    createFallbackManagers() {
        console.log('Creating fallback managers...');

        // Create minimal fallback managers to prevent errors
        if (!window.AuthManager) {
            window.AuthManager = {
                isSignedIn: () => false,
                getCurrentUser: () => null,
                signInWithEmail: () => Promise.reject(new Error('Auth not available')),
                signInWithGoogle: () => Promise.reject(new Error('Auth not available')),
                signOut: () => Promise.reject(new Error('Auth not available'))
            };
        }

        if (!window.StorageManager) {
            window.StorageManager = {
                getUserData: () => null,
                setUserData: () => {},
                getMessages: () => [],
                saveMessage: () => null
            };
        }

        if (!window.ChatManager) {
            window.ChatManager = {
                init: () => {},
                cleanup: () => {},
                isConnected: false
            };
        }

        if (!window.UIManager) {
            window.UIManager = {
                showLoginScreen: () => {},
                showChatScreen: () => {},
                showError: (msg) => alert('Error: ' + msg)
            };
        }
    }

    setupErrorHandlers() {
        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.handleError(event.error);
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.handleError(event.reason);
        });

        // Firebase auth error handler
        if (window.auth) {
            window.auth.onAuthStateChanged(null, (error) => {
                console.error('Auth state error:', error);
                this.handleAuthError(error);
            });
        }
    }

    setupAppEventListeners() {
        // Page visibility change (for user status)
        document.addEventListener('visibilitychange', () => {
            if (AuthManager.isSignedIn()) {
                const status = document.hidden ? 
                    APP_CONFIG.USER_STATUS.AWAY : 
                    APP_CONFIG.USER_STATUS.ONLINE;
                AuthManager.updateUserStatus(status);
            }
        });

        // Before page unload (cleanup)
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });

        // Online/offline status
        window.addEventListener('online', () => {
            console.log('App is online');
            if (ChatManager && !ChatManager.isConnected) {
                ChatManager.connectSocket();
            }
        });

        window.addEventListener('offline', () => {
            console.log('App is offline');
            UIManager.updateConnectionStatus(false);
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (event) => {
            this.handleKeyboardShortcuts(event);
        });
    }

    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + K: Focus search
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            const searchInput = document.querySelector('.search-bar input:not([style*="display: none"])');
            if (searchInput) {
                searchInput.focus();
            }
        }

        // Escape: Close modals
        if (event.key === 'Escape') {
            const openModal = document.querySelector('.modal:not(.hidden)');
            if (openModal) {
                openModal.classList.add('hidden');
            }
        }

        // Ctrl/Cmd + Enter: Send message
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            const messageInput = document.getElementById('messageInput');
            if (messageInput === document.activeElement) {
                UIManager.handleSendMessage();
            }
        }
    }

    checkForUpdates() {
        // Check if there's a newer version available
        const currentVersion = APP_CONFIG.VERSION;
        const storedVersion = localStorage.getItem('app_version');
        
        if (storedVersion && storedVersion !== currentVersion) {
            console.log(`App updated from ${storedVersion} to ${currentVersion}`);
            this.handleAppUpdate(storedVersion, currentVersion);
        }
        
        localStorage.setItem('app_version', currentVersion);
    }

    handleAppUpdate(oldVersion, newVersion) {
        // Handle app updates (migrations, notifications, etc.)
        console.log('Handling app update...');
        
        // You can add migration logic here if needed
        // For example, updating storage structure, clearing cache, etc.
        
        // Show update notification
        setTimeout(() => {
            UIManager.showSuccess(`App updated to version ${newVersion}!`);
        }, 2000);
    }

    handleError(error) {
        console.error('App error:', error);
        
        // Don't show error messages for network errors in offline mode
        if (!navigator.onLine && error.message.includes('network')) {
            return;
        }
        
        // Show user-friendly error message
        let message = 'An unexpected error occurred';
        
        if (error.message) {
            // Filter out technical details for user
            if (error.message.includes('Firebase') || error.message.includes('auth')) {
                message = 'Authentication error. Please try signing in again.';
            } else if (error.message.includes('network') || error.message.includes('fetch')) {
                message = 'Network error. Please check your connection.';
            } else if (error.message.includes('storage') || error.message.includes('localStorage')) {
                message = 'Storage error. Please clear your browser data and try again.';
            } else {
                message = error.message;
            }
        }
        
        UIManager.showError(message);
    }

    handleAuthError(error) {
        console.error('Auth error:', error);
        
        // Handle specific auth errors
        if (error.code === 'auth/network-request-failed') {
            UIManager.showError('Network error. Please check your connection.');
        } else if (error.code === 'auth/too-many-requests') {
            UIManager.showError('Too many failed attempts. Please try again later.');
        } else {
            UIManager.showError('Authentication error. Please try signing in again.');
        }
    }

    handleInitError(error) {
        console.error('Initialization error:', error);
        
        // Show critical error message
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: #f8d7da;
            color: #721c24;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #f5c6cb;
            max-width: 400px;
            text-align: center;
            z-index: 9999;
        `;
        errorDiv.innerHTML = `
            <h3>Failed to Initialize App</h3>
            <p>${error.message || 'Unknown error occurred'}</p>
            <button onclick="location.reload()" style="
                background: #721c24;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                margin-top: 10px;
            ">Reload Page</button>
        `;
        document.body.appendChild(errorDiv);
    }

    cleanup() {
        console.log('Cleaning up app...');
        
        try {
            // Update user status to offline
            if (AuthManager.isSignedIn()) {
                AuthManager.updateUserStatus(APP_CONFIG.USER_STATUS.OFFLINE);
            }
            
            // Cleanup chat manager
            if (ChatManager) {
                ChatManager.cleanup();
            }
            
            // Clear any timeouts/intervals
            if (UIManager.typingTimeout) {
                clearTimeout(UIManager.typingTimeout);
            }
            
        } catch (error) {
            console.error('Error during cleanup:', error);
        }
    }

    // Public methods for debugging/testing
    getAppInfo() {
        return {
            version: APP_CONFIG.VERSION,
            initialized: this.isInitialized,
            user: AuthManager.getCurrentUser(),
            storageInfo: StorageManager.getStorageInfo(),
            connectionStatus: ChatManager.isConnected,
            activeUsers: ChatManager.getActiveUsers().length
        };
    }

    exportData() {
        return StorageManager.exportData();
    }

    importData(jsonData) {
        return StorageManager.importData(jsonData);
    }

    clearAllData() {
        if (confirm('Are you sure you want to clear all data? This cannot be undone.')) {
            StorageManager.clearAllData();
            location.reload();
        }
    }
}

// Initialize app when script loads
const app = new App();

// Make app instance globally available for debugging
window.ChatApp = app;

// Service Worker registration (for future PWA features)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
                console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
