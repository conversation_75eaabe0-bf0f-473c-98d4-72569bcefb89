{"name": "chat-app", "version": "1.0.0", "description": "Real-time chat application with Firebase authentication and Socket.IO", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "setup": "node setup.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["chat", "socket.io", "firebase", "real-time", "messaging"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "cors": "^2.8.5", "helmet": "^7.0.0", "compression": "^1.7.4", "morgan": "^1.10.0", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}