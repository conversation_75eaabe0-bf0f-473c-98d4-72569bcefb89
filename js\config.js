// Firebase Configuration
// Replace with your Firebase config
const firebaseConfig = {
  apiKey: "AIzaSyDJbzXKl6RU1CxnD6Dg3uyHYjIUfPA2fu8",
  authDomain: "chatapp00001-e14e4.firebaseapp.com",
  projectId: "chatapp00001-e14e4",
  storageBucket: "chatapp00001-e14e4.firebasestorage.app",
  messagingSenderId: "603037921311",
  appId: "1:603037921311:web:69c062787f5d9464ab652a",
  measurementId: "G-ZLL2N2VCW9"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Firebase services
const auth = firebase.auth();
const db = firebase.firestore();

// Google Auth Provider
const googleProvider = new firebase.auth.GoogleAuthProvider();

// App Configuration
const APP_CONFIG = {
    APP_NAME: 'Chat App',
    VERSION: '1.0.0',
    MAX_MESSAGE_LENGTH: 1000,
    MAX_GROUP_MEMBERS: 50,
    STORAGE_KEYS: {
        USER_DATA: 'chatapp_user_data',
        MESSAGES: 'chatapp_messages',
        CHATS: 'chatapp_chats',
        GROUPS: 'chatapp_groups',
        SETTINGS: 'chatapp_settings'
    },
    DEFAULT_AVATAR: 'https://via.placeholder.com/40x40/667eea/ffffff?text=U',
    MESSAGE_TYPES: {
        TEXT: 'text',
        IMAGE: 'image',
        FILE: 'file',
        SYSTEM: 'system'
    },
    CHAT_TYPES: {
        DIRECT: 'direct',
        GROUP: 'group'
    },
    USER_STATUS: {
        ONLINE: 'online',
        OFFLINE: 'offline',
        AWAY: 'away'
    }
};

// Export for use in other files
window.APP_CONFIG = APP_CONFIG;
window.auth = auth;
window.db = db;
window.googleProvider = googleProvider;
