// Firebase Configuration
// Replace with your Firebase config
const firebaseConfig = {
    apiKey: "your-api-key-here",
    authDomain: "your-project-id.firebaseapp.com",
    projectId: "your-project-id",
    storageBucket: "your-project-id.appspot.com",
    messagingSenderId: "your-sender-id",
    appId: "your-app-id"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Firebase services
const auth = firebase.auth();
const db = firebase.firestore();

// Google Auth Provider
const googleProvider = new firebase.auth.GoogleAuthProvider();

// App Configuration
const APP_CONFIG = {
    APP_NAME: 'Chat App',
    VERSION: '1.0.0',
    MAX_MESSAGE_LENGTH: 1000,
    MAX_GROUP_MEMBERS: 50,
    STORAGE_KEYS: {
        USER_DATA: 'chatapp_user_data',
        MESSAGES: 'chatapp_messages',
        CHATS: 'chatapp_chats',
        GROUPS: 'chatapp_groups',
        SETTINGS: 'chatapp_settings'
    },
    DEFAULT_AVATAR: 'https://via.placeholder.com/40x40/667eea/ffffff?text=U',
    MESSAGE_TYPES: {
        TEXT: 'text',
        IMAGE: 'image',
        FILE: 'file',
        SYSTEM: 'system'
    },
    CHAT_TYPES: {
        DIRECT: 'direct',
        GROUP: 'group'
    },
    USER_STATUS: {
        ONLINE: 'online',
        OFFLINE: 'offline',
        AWAY: 'away'
    }
};

// Export for use in other files
window.APP_CONFIG = APP_CONFIG;
window.auth = auth;
window.db = db;
window.googleProvider = googleProvider;
