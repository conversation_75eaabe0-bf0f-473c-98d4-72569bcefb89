// Authentication Module
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.authStateListeners = [];
        this.init();
    }

    init() {
        // Listen for auth state changes
        auth.onAuthStateChanged((user) => {
            this.currentUser = user;
            this.handleAuthStateChange(user);
        });
    }

    // Handle authentication state changes
    handleAuthStateChange(user) {
        if (user) {
            // User is signed in
            this.onUserSignedIn(user);
        } else {
            // User is signed out
            this.onUserSignedOut();
        }

        // Notify listeners
        this.authStateListeners.forEach(listener => listener(user));
    }

    // User signed in handler
    async onUserSignedIn(user) {
        try {
            // Save user data to localStorage
            const userData = {
                uid: user.uid,
                email: user.email,
                displayName: user.displayName || user.email.split('@')[0],
                photoURL: user.photoURL || APP_CONFIG.DEFAULT_AVATAR,
                lastSeen: new Date().toISOString(),
                status: APP_CONFIG.USER_STATUS.ONLINE
            };

            StorageManager.setUserData(userData);
            
            // Update user status to online
            this.updateUserStatus(APP_CONFIG.USER_STATUS.ONLINE);
            
            // Show chat screen
            UIManager.showChatScreen();
            
            // Initialize chat functionality
            ChatManager.init();
            
            console.log('User signed in:', userData);
        } catch (error) {
            console.error('Error handling user sign in:', error);
            UIManager.showError('Failed to initialize user session');
        }
    }

    // User signed out handler
    onUserSignedOut() {
        // Clear user data
        StorageManager.clearUserData();
        
        // Show login screen
        UIManager.showLoginScreen();
        
        // Clean up chat manager
        if (window.ChatManager) {
            ChatManager.cleanup();
        }
        
        console.log('User signed out');
    }

    // Sign in with email and password
    async signInWithEmail(email, password) {
        try {
            UIManager.showLoading('Signing in...');
            const result = await auth.signInWithEmailAndPassword(email, password);
            UIManager.hideLoading();
            return result.user;
        } catch (error) {
            UIManager.hideLoading();
            throw this.handleAuthError(error);
        }
    }

    // Sign up with email and password
    async signUpWithEmail(email, password, displayName) {
        try {
            UIManager.showLoading('Creating account...');
            const result = await auth.createUserWithEmailAndPassword(email, password);
            
            // Update user profile
            await result.user.updateProfile({
                displayName: displayName
            });
            
            UIManager.hideLoading();
            return result.user;
        } catch (error) {
            UIManager.hideLoading();
            throw this.handleAuthError(error);
        }
    }

    // Sign in with Google
    async signInWithGoogle() {
        try {
            UIManager.showLoading('Signing in with Google...');
            const result = await auth.signInWithPopup(googleProvider);
            UIManager.hideLoading();
            return result.user;
        } catch (error) {
            UIManager.hideLoading();
            throw this.handleAuthError(error);
        }
    }

    // Sign out
    async signOut() {
        try {
            // Update user status to offline
            await this.updateUserStatus(APP_CONFIG.USER_STATUS.OFFLINE);
            
            // Sign out from Firebase
            await auth.signOut();
        } catch (error) {
            console.error('Error signing out:', error);
            throw error;
        }
    }

    // Update user status
    async updateUserStatus(status) {
        if (!this.currentUser) return;

        try {
            const userData = StorageManager.getUserData();
            if (userData) {
                userData.status = status;
                userData.lastSeen = new Date().toISOString();
                StorageManager.setUserData(userData);
            }
        } catch (error) {
            console.error('Error updating user status:', error);
        }
    }

    // Update user profile
    async updateProfile(profileData) {
        if (!this.currentUser) throw new Error('No user signed in');

        try {
            // Update Firebase profile
            await this.currentUser.updateProfile({
                displayName: profileData.displayName,
                photoURL: profileData.photoURL
            });

            // Update local storage
            const userData = StorageManager.getUserData();
            if (userData) {
                userData.displayName = profileData.displayName;
                userData.photoURL = profileData.photoURL;
                userData.status = profileData.status || userData.status;
                StorageManager.setUserData(userData);
            }

            return true;
        } catch (error) {
            console.error('Error updating profile:', error);
            throw error;
        }
    }

    // Handle authentication errors
    handleAuthError(error) {
        let message = 'An error occurred during authentication';
        
        switch (error.code) {
            case 'auth/user-not-found':
                message = 'No account found with this email';
                break;
            case 'auth/wrong-password':
                message = 'Incorrect password';
                break;
            case 'auth/email-already-in-use':
                message = 'An account with this email already exists';
                break;
            case 'auth/weak-password':
                message = 'Password should be at least 6 characters';
                break;
            case 'auth/invalid-email':
                message = 'Invalid email address';
                break;
            case 'auth/popup-closed-by-user':
                message = 'Sign-in popup was closed';
                break;
            case 'auth/network-request-failed':
                message = 'Network error. Please check your connection';
                break;
            default:
                message = error.message;
        }
        
        return new Error(message);
    }

    // Add auth state listener
    addAuthStateListener(listener) {
        this.authStateListeners.push(listener);
    }

    // Remove auth state listener
    removeAuthStateListener(listener) {
        const index = this.authStateListeners.indexOf(listener);
        if (index > -1) {
            this.authStateListeners.splice(index, 1);
        }
    }

    // Get current user data
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if user is signed in
    isSignedIn() {
        return !!this.currentUser;
    }
}

// Create global instance
window.AuthManager = new AuthManager();
