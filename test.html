<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat App - Test Page</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #667eea;
            margin-top: 0;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .test-pass {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5a6fd8;
        }
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-online { background: #28a745; }
        .status-offline { background: #dc3545; }
        .status-connecting { background: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Chat App Test Suite</h1>
        <p>This page helps you test various features of the Chat App before deployment.</p>

        <!-- Connection Status -->
        <div class="test-section">
            <h3>🔌 Connection Status</h3>
            <div id="connectionStatus">
                <span class="status-indicator status-connecting"></span>
                <span id="connectionText">Checking connection...</span>
            </div>
            <button onclick="testConnection()">Test Connection</button>
        </div>

        <!-- Firebase Test -->
        <div class="test-section">
            <h3>🔥 Firebase Configuration</h3>
            <div id="firebaseTest">
                <div class="test-info">Testing Firebase configuration...</div>
            </div>
            <button onclick="testFirebase()">Test Firebase</button>
        </div>

        <!-- Local Storage Test -->
        <div class="test-section">
            <h3>💾 Local Storage</h3>
            <div id="storageTest">
                <div class="test-info">Testing local storage functionality...</div>
            </div>
            <button onclick="testStorage()">Test Storage</button>
            <button onclick="clearStorage()">Clear Storage</button>
        </div>

        <!-- Socket.IO Test -->
        <div class="test-section">
            <h3>⚡ Socket.IO Connection</h3>
            <div id="socketTest">
                <div class="test-info">Testing Socket.IO connection...</div>
            </div>
            <button onclick="testSocket()">Test Socket</button>
        </div>

        <!-- UI Components Test -->
        <div class="test-section">
            <h3>🎨 UI Components</h3>
            <div id="uiTest">
                <div class="test-info">Testing UI components...</div>
            </div>
            <button onclick="testUI()">Test UI</button>
        </div>

        <!-- Performance Test -->
        <div class="test-section">
            <h3>⚡ Performance</h3>
            <div id="performanceTest">
                <div class="test-info">Testing application performance...</div>
            </div>
            <button onclick="testPerformance()">Test Performance</button>
        </div>

        <!-- System Info -->
        <div class="test-section">
            <h3>ℹ️ System Information</h3>
            <div id="systemInfo">
                <div class="test-info">Loading system information...</div>
            </div>
            <button onclick="loadSystemInfo()">Refresh Info</button>
        </div>

        <!-- Test All -->
        <div class="test-section">
            <h3>🚀 Run All Tests</h3>
            <button onclick="runAllTests()" style="background: #28a745; font-size: 16px; padding: 15px 30px;">
                Run All Tests
            </button>
            <button onclick="location.href='index.html'" style="background: #17a2b8;">
                Go to Chat App
            </button>
        </div>
    </div>

    <!-- Include necessary scripts -->
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <script src="js/config.js"></script>

    <script>
        // Test Functions
        function updateStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const className = type === 'pass' ? 'test-pass' : type === 'fail' ? 'test-fail' : 'test-info';
            element.innerHTML = `<div class="${className}">${message}</div>`;
        }

        function testConnection() {
            updateStatus('connectionStatus', 'Testing server connection...', 'info');
            
            fetch('/api/health')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('connectionText').textContent = 'Connected to server';
                    document.querySelector('.status-indicator').className = 'status-indicator status-online';
                    updateStatus('connectionStatus', `✅ Server is running! Active users: ${data.activeUsers}, Chat rooms: ${data.chatRooms}`, 'pass');
                })
                .catch(error => {
                    document.getElementById('connectionText').textContent = 'Connection failed';
                    document.querySelector('.status-indicator').className = 'status-indicator status-offline';
                    updateStatus('connectionStatus', `❌ Server connection failed: ${error.message}`, 'fail');
                });
        }

        function testFirebase() {
            updateStatus('firebaseTest', 'Testing Firebase configuration...', 'info');
            
            try {
                if (typeof firebase === 'undefined') {
                    throw new Error('Firebase SDK not loaded');
                }
                
                if (!window.auth) {
                    throw new Error('Firebase Auth not initialized');
                }
                
                // Test if configuration is set
                const config = firebase.app().options;
                if (config.apiKey === 'your-api-key-here') {
                    throw new Error('Firebase not configured. Run "npm run setup" to configure.');
                }
                
                updateStatus('firebaseTest', `✅ Firebase configured successfully! Project: ${config.projectId}`, 'pass');
            } catch (error) {
                updateStatus('firebaseTest', `❌ Firebase test failed: ${error.message}`, 'fail');
            }
        }

        function testStorage() {
            updateStatus('storageTest', 'Testing local storage...', 'info');
            
            try {
                // Test localStorage availability
                if (typeof Storage === 'undefined') {
                    throw new Error('Local storage not supported');
                }
                
                // Test write/read
                const testKey = 'chatapp_test';
                const testData = { test: true, timestamp: Date.now() };
                localStorage.setItem(testKey, JSON.stringify(testData));
                
                const retrieved = JSON.parse(localStorage.getItem(testKey));
                if (!retrieved || !retrieved.test) {
                    throw new Error('Failed to read/write localStorage');
                }
                
                localStorage.removeItem(testKey);
                
                // Check storage usage
                let totalSize = 0;
                for (let key in localStorage) {
                    if (localStorage.hasOwnProperty(key)) {
                        totalSize += localStorage[key].length;
                    }
                }
                
                updateStatus('storageTest', `✅ Local storage working! Current usage: ${(totalSize / 1024).toFixed(2)} KB`, 'pass');
            } catch (error) {
                updateStatus('storageTest', `❌ Storage test failed: ${error.message}`, 'fail');
            }
        }

        function clearStorage() {
            if (confirm('Are you sure you want to clear all stored data?')) {
                localStorage.clear();
                updateStatus('storageTest', '🗑️ All local storage cleared', 'info');
            }
        }

        function testSocket() {
            updateStatus('socketTest', 'Testing Socket.IO connection...', 'info');
            
            try {
                const socket = io('http://localhost:3000', {
                    timeout: 5000,
                    transports: ['websocket', 'polling']
                });
                
                socket.on('connect', () => {
                    updateStatus('socketTest', `✅ Socket.IO connected! ID: ${socket.id}`, 'pass');
                    socket.disconnect();
                });
                
                socket.on('connect_error', (error) => {
                    updateStatus('socketTest', `❌ Socket.IO connection failed: ${error.message}`, 'fail');
                });
                
                setTimeout(() => {
                    if (!socket.connected) {
                        updateStatus('socketTest', '❌ Socket.IO connection timeout', 'fail');
                        socket.disconnect();
                    }
                }, 5000);
                
            } catch (error) {
                updateStatus('socketTest', `❌ Socket.IO test failed: ${error.message}`, 'fail');
            }
        }

        function testUI() {
            updateStatus('uiTest', 'Testing UI components...', 'info');
            
            try {
                // Test if main elements exist
                const requiredElements = ['loginScreen', 'chatScreen'];
                const missingElements = [];
                
                requiredElements.forEach(id => {
                    if (!document.getElementById(id)) {
                        missingElements.push(id);
                    }
                });
                
                if (missingElements.length > 0) {
                    throw new Error(`Missing UI elements: ${missingElements.join(', ')}`);
                }
                
                // Test CSS loading
                const testElement = document.createElement('div');
                testElement.className = 'btn-primary';
                document.body.appendChild(testElement);
                const styles = window.getComputedStyle(testElement);
                document.body.removeChild(testElement);
                
                if (!styles.backgroundColor || styles.backgroundColor === 'rgba(0, 0, 0, 0)') {
                    throw new Error('CSS styles not loaded properly');
                }
                
                updateStatus('uiTest', '✅ UI components loaded successfully!', 'pass');
            } catch (error) {
                updateStatus('uiTest', `❌ UI test failed: ${error.message}`, 'fail');
            }
        }

        function testPerformance() {
            updateStatus('performanceTest', 'Testing performance...', 'info');
            
            const startTime = performance.now();
            
            // Simulate some operations
            setTimeout(() => {
                const endTime = performance.now();
                const loadTime = endTime - startTime;
                
                let message = `✅ Performance test completed in ${loadTime.toFixed(2)}ms`;
                let type = 'pass';
                
                if (loadTime > 1000) {
                    message = `⚠️ Performance test completed in ${loadTime.toFixed(2)}ms (slower than expected)`;
                    type = 'info';
                }
                
                // Add memory usage if available
                if (performance.memory) {
                    const memoryMB = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
                    message += ` | Memory usage: ${memoryMB} MB`;
                }
                
                updateStatus('performanceTest', message, type);
            }, 100);
        }

        function loadSystemInfo() {
            const info = {
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                screenResolution: `${screen.width}x${screen.height}`,
                windowSize: `${window.innerWidth}x${window.innerHeight}`,
                localStorage: typeof Storage !== 'undefined',
                webSocket: typeof WebSocket !== 'undefined',
                serviceWorker: 'serviceWorker' in navigator
            };
            
            let infoHtml = '<div class="test-info">';
            for (const [key, value] of Object.entries(info)) {
                infoHtml += `<strong>${key}:</strong> ${value}<br>`;
            }
            infoHtml += '</div>';
            
            document.getElementById('systemInfo').innerHTML = infoHtml;
        }

        async function runAllTests() {
            updateStatus('connectionStatus', 'Running all tests...', 'info');
            
            testConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testFirebase();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testStorage();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testSocket();
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            testUI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testPerformance();
            loadSystemInfo();
            
            console.log('All tests completed!');
        }

        // Auto-load system info on page load
        window.addEventListener('load', () => {
            loadSystemInfo();
            testConnection();
        });
    </script>
</body>
</html>
