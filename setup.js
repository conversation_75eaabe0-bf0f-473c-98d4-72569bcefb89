#!/usr/bin/env node

/**
 * Setup Script for Chat App
 * This script helps users configure Firebase settings
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

console.log('🚀 Welcome to Chat App Setup!');
console.log('This script will help you configure Firebase for your chat application.\n');

console.log('📋 Before proceeding, make sure you have:');
console.log('1. Created a Firebase project at https://console.firebase.google.com/');
console.log('2. Enabled Authentication (Email/Password and Google)');
console.log('3. Obtained your Firebase configuration object\n');

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function setupFirebase() {
    try {
        console.log('🔧 Let\'s configure your Firebase settings:\n');

        const apiKey = await question('Enter your Firebase API Key: ');
        const authDomain = await question('Enter your Auth Domain (e.g., your-project.firebaseapp.com): ');
        const projectId = await question('Enter your Project ID: ');
        const storageBucket = await question('Enter your Storage Bucket (e.g., your-project.appspot.com): ');
        const messagingSenderId = await question('Enter your Messaging Sender ID: ');
        const appId = await question('Enter your App ID: ');

        if (!apiKey || !authDomain || !projectId || !storageBucket || !messagingSenderId || !appId) {
            console.log('❌ All fields are required. Please run the setup again.');
            process.exit(1);
        }

        const firebaseConfig = `// Firebase Configuration
// Replace with your Firebase config
const firebaseConfig = {
    apiKey: "${apiKey}",
    authDomain: "${authDomain}",
    projectId: "${projectId}",
    storageBucket: "${storageBucket}",
    messagingSenderId: "${messagingSenderId}",
    appId: "${appId}"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Firebase services
const auth = firebase.auth();
const db = firebase.firestore();

// Google Auth Provider
const googleProvider = new firebase.auth.GoogleAuthProvider();

// App Configuration
const APP_CONFIG = {
    APP_NAME: 'Chat App',
    VERSION: '1.0.0',
    MAX_MESSAGE_LENGTH: 1000,
    MAX_GROUP_MEMBERS: 50,
    STORAGE_KEYS: {
        USER_DATA: 'chatapp_user_data',
        MESSAGES: 'chatapp_messages',
        CHATS: 'chatapp_chats',
        GROUPS: 'chatapp_groups',
        SETTINGS: 'chatapp_settings'
    },
    DEFAULT_AVATAR: 'https://via.placeholder.com/40x40/667eea/ffffff?text=U',
    MESSAGE_TYPES: {
        TEXT: 'text',
        IMAGE: 'image',
        FILE: 'file',
        SYSTEM: 'system'
    },
    CHAT_TYPES: {
        DIRECT: 'direct',
        GROUP: 'group'
    },
    USER_STATUS: {
        ONLINE: 'online',
        OFFLINE: 'offline',
        AWAY: 'away'
    }
};

// Export for use in other files
window.APP_CONFIG = APP_CONFIG;
window.auth = auth;
window.db = db;
window.googleProvider = googleProvider;`;

        // Write to config file
        const configPath = path.join(__dirname, 'js', 'config.js');
        fs.writeFileSync(configPath, firebaseConfig);

        console.log('\n✅ Firebase configuration saved successfully!');
        console.log('📁 Updated file: js/config.js\n');

        console.log('🎉 Setup complete! You can now:');
        console.log('1. Run "npm start" to start the server');
        console.log('2. Open http://localhost:3000 in your browser');
        console.log('3. Create an account or sign in with Google\n');

        console.log('📚 Additional setup steps:');
        console.log('1. Add your domain to Firebase authorized domains');
        console.log('2. Configure Firebase security rules if needed');
        console.log('3. Set up Firebase hosting for production deployment\n');

        console.log('🔗 Useful links:');
        console.log('- Firebase Console: https://console.firebase.google.com/');
        console.log('- Documentation: https://firebase.google.com/docs');
        console.log('- Support: Check the README.md file\n');

    } catch (error) {
        console.error('❌ Setup failed:', error.message);
        process.exit(1);
    } finally {
        rl.close();
    }
}

async function main() {
    const proceed = await question('Do you want to proceed with Firebase setup? (y/n): ');
    
    if (proceed.toLowerCase() === 'y' || proceed.toLowerCase() === 'yes') {
        await setupFirebase();
    } else {
        console.log('Setup cancelled. You can run this script again anytime with: node setup.js');
        rl.close();
    }
}

// Check if config file exists
const configPath = path.join(__dirname, 'js', 'config.js');
if (fs.existsSync(configPath)) {
    const content = fs.readFileSync(configPath, 'utf8');
    if (content.includes('your-api-key-here')) {
        console.log('⚠️  Firebase configuration not set up yet.\n');
        main();
    } else {
        console.log('✅ Firebase is already configured!');
        console.log('If you want to reconfigure, delete js/config.js and run this script again.\n');
        rl.close();
    }
} else {
    console.log('❌ Config file not found. Please ensure you\'re in the correct directory.\n');
    rl.close();
}
