<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat App</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="screen">
        <div class="login-container">
            <div class="login-header">
                <i class="fas fa-comments"></i>
                <h1>Chat App</h1>
                <p>Connect with friends and family</p>
            </div>
            
            <div class="login-form">
                <div class="form-group">
                    <input type="email" id="email" placeholder="Email" required>
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="form-group">
                    <input type="password" id="password" placeholder="Password" required>
                    <i class="fas fa-lock"></i>
                </div>
                
                <button id="loginBtn" class="btn-primary">
                    <i class="fas fa-sign-in-alt"></i>
                    Sign In
                </button>
                
                <div class="divider">
                    <span>or</span>
                </div>
                
                <button id="googleLoginBtn" class="btn-google">
                    <i class="fab fa-google"></i>
                    Continue with Google
                </button>
                
                <div class="auth-switch">
                    <p>Don't have an account? <span id="switchToSignup">Sign up</span></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Chat Interface -->
    <div id="chatScreen" class="screen hidden">
        <div class="chat-container">
            <!-- Sidebar -->
            <div class="sidebar">
                <div class="sidebar-header">
                    <div class="user-info">
                        <img id="userAvatar" src="" alt="User Avatar" class="avatar">
                        <div class="user-details">
                            <h3 id="userName">User Name</h3>
                            <span id="userStatus" class="status online">Online</span>
                        </div>
                    </div>
                    <div class="sidebar-actions">
                        <button id="profileBtn" class="icon-btn" title="Profile">
                            <i class="fas fa-user"></i>
                        </button>
                        <button id="settingsBtn" class="icon-btn" title="Settings">
                            <i class="fas fa-cog"></i>
                        </button>
                        <button id="logoutBtn" class="icon-btn" title="Logout">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </div>
                </div>
                
                <div class="sidebar-tabs">
                    <button class="tab-btn active" data-tab="chats">
                        <i class="fas fa-comments"></i>
                        Chats
                    </button>
                    <button class="tab-btn" data-tab="users">
                        <i class="fas fa-users"></i>
                        Users
                    </button>
                    <button class="tab-btn" data-tab="groups">
                        <i class="fas fa-layer-group"></i>
                        Groups
                    </button>
                </div>
                
                <div class="sidebar-content">
                    <!-- Chat List -->
                    <div id="chatsList" class="tab-content active">
                        <div class="search-bar">
                            <input type="text" id="searchChats" placeholder="Search chats...">
                            <i class="fas fa-search"></i>
                        </div>
                        <div id="chatsContainer" class="chats-container">
                            <!-- Chat items will be populated here -->
                        </div>
                    </div>
                    
                    <!-- Users List -->
                    <div id="usersList" class="tab-content">
                        <div class="search-bar">
                            <input type="text" id="searchUsers" placeholder="Search users...">
                            <i class="fas fa-search"></i>
                        </div>
                        <div id="usersContainer" class="users-container">
                            <!-- User items will be populated here -->
                        </div>
                    </div>
                    
                    <!-- Groups List -->
                    <div id="groupsList" class="tab-content">
                        <div class="groups-header">
                            <button id="createGroupBtn" class="btn-secondary">
                                <i class="fas fa-plus"></i>
                                Create Group
                            </button>
                        </div>
                        <div id="groupsContainer" class="groups-container">
                            <!-- Group items will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Chat Area -->
            <div class="main-chat">
                <div id="welcomeScreen" class="welcome-screen">
                    <div class="welcome-content">
                        <i class="fas fa-comments"></i>
                        <h2>Welcome to Chat App</h2>
                        <p>Select a chat to start messaging</p>
                    </div>
                </div>
                
                <div id="chatArea" class="chat-area hidden">
                    <div class="chat-header">
                        <div class="chat-info">
                            <img id="chatAvatar" src="" alt="Chat Avatar" class="avatar">
                            <div class="chat-details">
                                <h3 id="chatName">Chat Name</h3>
                                <span id="chatStatus" class="status">Status</span>
                            </div>
                        </div>
                        <div class="chat-actions">
                            <button class="icon-btn" title="Call">
                                <i class="fas fa-phone"></i>
                            </button>
                            <button class="icon-btn" title="Video Call">
                                <i class="fas fa-video"></i>
                            </button>
                            <button class="icon-btn" title="More">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div id="messagesContainer" class="messages-container">
                        <!-- Messages will be populated here -->
                    </div>
                    
                    <div class="message-input-container">
                        <div class="message-input">
                            <button class="icon-btn" title="Attach">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <input type="text" id="messageInput" placeholder="Type a message...">
                            <button class="icon-btn" title="Emoji">
                                <i class="fas fa-smile"></i>
                            </button>
                            <button id="sendBtn" class="send-btn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Modal -->
    <div id="profileModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Profile</h2>
                <button class="close-btn" data-modal="profileModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="profile-avatar">
                    <img id="profileAvatar" src="" alt="Profile Avatar" class="avatar large">
                    <button id="changeAvatarBtn" class="change-avatar-btn">
                        <i class="fas fa-camera"></i>
                    </button>
                </div>
                <div class="profile-info">
                    <div class="form-group">
                        <label>Name</label>
                        <input type="text" id="profileName" placeholder="Your name">
                    </div>
                    <div class="form-group">
                        <label>Email</label>
                        <input type="email" id="profileEmail" readonly>
                    </div>
                    <div class="form-group">
                        <label>Status</label>
                        <input type="text" id="profileStatus" placeholder="Your status">
                    </div>
                </div>
                <div class="modal-actions">
                    <button id="saveProfileBtn" class="btn-primary">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Group Creation Modal -->
    <div id="groupModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Create Group</h2>
                <button class="close-btn" data-modal="groupModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>Group Name</label>
                    <input type="text" id="groupName" placeholder="Enter group name">
                </div>
                <div class="form-group">
                    <label>Group Description</label>
                    <textarea id="groupDescription" placeholder="Enter group description"></textarea>
                </div>
                <div class="form-group">
                    <label>Add Members</label>
                    <div id="membersList" class="members-list">
                        <!-- Available users will be listed here -->
                    </div>
                </div>
                <div class="modal-actions">
                    <button id="createGroupConfirmBtn" class="btn-primary">Create Group</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.22.0/firebase-firestore-compat.js"></script>
    
    <!-- App Scripts -->
    <script src="js/config.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/chat.js"></script>
    <script src="js/ui.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
